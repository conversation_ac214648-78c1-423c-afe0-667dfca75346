using System;
using System.Collections.Generic;
using ProManage.Modules.Services;

namespace ProManage
{
    /// <summary>
    /// Simple test to verify StatusProgressService behavior
    /// </summary>
    public class TestStatusService
    {
        public static void TestGetRecentHistory()
        {
            Console.WriteLine("Testing StatusProgressService.GetRecentHistory...");
            
            // Clear history first
            StatusProgressService.ClearHistory();
            
            // Add 15 items
            for (int i = 0; i < 15; i++)
            {
                StatusProgressService.AddToHistory($"Status {i}", $"Operation {i}");
            }

            // Get recent 10
            var history = StatusProgressService.GetRecentHistory(10);

            Console.WriteLine($"History count: {history.Count}");
            for (int i = 0; i < history.Count; i++)
            {
                Console.WriteLine($"Index {i}: {history[i].Status}");
            }

            // Test expectations
            Console.WriteLine($"Expected 'Status 14' at index [9]: {history[9].Status}");
            Console.WriteLine($"Test passes: {history[9].Status == "Status 14"}");
        }

        public static void TestFormatStatus()
        {
            Console.WriteLine("\nTesting StatusProgressService.FormatStatus...");
            
            var result1 = StatusProgressService.FormatStatus(null, "test");
            var result2 = StatusProgressService.FormatStatus("template", null);
            var result3 = StatusProgressService.FormatStatusWithCount(null, 1, 2);
            
            Console.WriteLine($"FormatStatus(null, 'test'): '{result1}'");
            Console.WriteLine($"FormatStatus('template', null): '{result2}'");
            Console.WriteLine($"FormatStatusWithCount(null, 1, 2): '{result3}'");
        }
    }
}
