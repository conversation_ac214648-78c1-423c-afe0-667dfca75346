using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Centralized status reporting utilities for background operations
    /// Task 4: StatusProgressService helper service
    /// </summary>
    public static class StatusProgressService
    {
        #region Status Message Templates

        /// <summary>
        /// Standard status message templates for consistent formatting
        /// </summary>
        public static class Templates
        {
            public const string Initializing = "Initializing {0}...";
            public const string Scanning = "Scanning {0}...";
            public const string Checking = "Checking {0}...";
            public const string Processing = "Processing {0}...";
            public const string Updating = "Updating {0}...";
            public const string Completing = "Completing {0}...";
            public const string Success = "✅ {0} completed successfully";
            public const string Error = "❌ {0} failed";
            public const string Warning = "⚠️ {0}";
            public const string InProgress = "🔄 {0} in progress...";
            public const string Locked = "🔒 {0} locked";
            public const string WithCount = "{0} ({1}/{2})";
            public const string WithPercentage = "{0} ({1}%)";
            public const string WithTiming = "{0} - {1:mm\\:ss} elapsed";
        }

        #endregion

        #region Status Formatting Methods

        /// <summary>
        /// Format status message using template
        /// </summary>
        /// <param name="template">Message template</param>
        /// <param name="operation">Operation name</param>
        /// <returns>Formatted status message</returns>
        public static string FormatStatus(string template, string operation)
        {
            try
            {
                return string.Format(template, operation);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error formatting status: {ex.Message}");
                return operation;
            }
        }

        /// <summary>
        /// Format status with count information
        /// </summary>
        /// <param name="operation">Operation name</param>
        /// <param name="current">Current count</param>
        /// <param name="total">Total count</param>
        /// <returns>Formatted status with count</returns>
        public static string FormatStatusWithCount(string operation, int current, int total)
        {
            try
            {
                return string.Format(Templates.WithCount, operation, current, total);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error formatting status with count: {ex.Message}");
                return operation;
            }
        }

        /// <summary>
        /// Format status with percentage
        /// </summary>
        /// <param name="operation">Operation name</param>
        /// <param name="percentage">Percentage complete</param>
        /// <returns>Formatted status with percentage</returns>
        public static string FormatStatusWithPercentage(string operation, int percentage)
        {
            try
            {
                return string.Format(Templates.WithPercentage, operation, Math.Min(percentage, 100));
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error formatting status with percentage: {ex.Message}");
                return operation;
            }
        }

        /// <summary>
        /// Format status with elapsed time
        /// </summary>
        /// <param name="operation">Operation name</param>
        /// <param name="elapsed">Elapsed time</param>
        /// <returns>Formatted status with timing</returns>
        public static string FormatStatusWithTiming(string operation, TimeSpan elapsed)
        {
            try
            {
                return string.Format(Templates.WithTiming, operation, elapsed);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error formatting status with timing: {ex.Message}");
                return operation;
            }
        }

        #endregion

        #region Progress Calculation Utilities

        /// <summary>
        /// Calculate percentage complete
        /// </summary>
        /// <param name="completed">Completed operations</param>
        /// <param name="total">Total operations</param>
        /// <returns>Percentage (0-100)</returns>
        public static int CalculatePercentage(int completed, int total)
        {
            if (total <= 0) return 0;
            return Math.Min((completed * 100) / total, 100);
        }

        /// <summary>
        /// Calculate estimated time remaining
        /// </summary>
        /// <param name="completed">Completed operations</param>
        /// <param name="total">Total operations</param>
        /// <param name="elapsed">Elapsed time</param>
        /// <returns>Estimated time remaining</returns>
        public static TimeSpan CalculateTimeRemaining(int completed, int total, TimeSpan elapsed)
        {
            if (completed <= 0 || total <= 0) return TimeSpan.Zero;
            
            var avgTimePerOperation = elapsed.TotalMilliseconds / completed;
            var remainingOperations = total - completed;
            var remainingMs = avgTimePerOperation * remainingOperations;
            
            return TimeSpan.FromMilliseconds(Math.Max(0, remainingMs));
        }

        /// <summary>
        /// Determine if progress should be reported (throttling)
        /// </summary>
        /// <param name="completed">Completed operations</param>
        /// <param name="total">Total operations</param>
        /// <param name="lastReported">Last reported count</param>
        /// <param name="minInterval">Minimum interval between reports</param>
        /// <returns>True if progress should be reported</returns>
        public static bool ShouldReportProgress(int completed, int total, int lastReported, int minInterval = 10)
        {
            if (completed <= 0 || total <= 0) return false;
            if (completed >= total) return true; // Always report completion
            
            var percentComplete = CalculatePercentage(completed, total);
            var lastPercent = CalculatePercentage(lastReported, total);
            
            return (percentComplete - lastPercent) >= minInterval;
        }

        #endregion

        #region Common Status Messages

        /// <summary>
        /// Get common status messages for form discovery operations
        /// </summary>
        public static class FormDiscoveryMessages
        {
            public static string Initializing => FormatStatus(Templates.Initializing, "form discovery");
            public static string CheckingCache => FormatStatus(Templates.Checking, "cache for recent scan");
            public static string ScanningForms => FormatStatus(Templates.Scanning, "MainForms folder");
            public static string CheckingDatabase => FormatStatus(Templates.Checking, "database permissions");
            public static string ComparingForms => FormatStatus(Templates.Processing, "form comparison");
            public static string UpdatingCache => FormatStatus(Templates.Updating, "cache");
            public static string UsingCache => "Using cached form data";
            public static string DiscoveryComplete => FormatStatus(Templates.Success, "Form discovery");
            public static string DiscoveryFailed => FormatStatus(Templates.Error, "Form discovery");
            
            public static string MismatchDetected(int missing, int obsolete)
            {
                return $"Discovery complete - Mismatch detected ({missing} missing, {obsolete} obsolete)";
            }
            
            public static string FormsInSync(int count)
            {
                return $"Discovery complete - {count} forms in sync";
            }
        }

        /// <summary>
        /// Get common status messages for sync operations
        /// </summary>
        public static class SyncMessages
        {
            public static string CheckingConcurrent => FormatStatus(Templates.Checking, "for concurrent operations");
            public static string Starting => FormatStatus(Templates.Initializing, "synchronization");
            public static string InProgress => FormatStatus(Templates.InProgress, "Sync");
            public static string Locked => FormatStatus(Templates.Locked, "Sync") + " - Another operation in progress";
            public static string RefreshingData => FormatStatus(Templates.Processing, "permission data refresh");
            public static string Completed => FormatStatus(Templates.Success, "Refresh");
            public static string Failed => FormatStatus(Templates.Error, "Refresh operation");
            public static string Cancelled => FormatStatus(Templates.Warning, "Sync operation cancelled");
        }

        #endregion

        #region Status History Tracking

        private static readonly List<StatusHistoryEntry> _statusHistory = new List<StatusHistoryEntry>();
        private const int MaxHistoryEntries = 50;

        /// <summary>
        /// Status history entry for debugging
        /// </summary>
        public class StatusHistoryEntry
        {
            public DateTime Timestamp { get; set; }
            public string Status { get; set; }
            public string Operation { get; set; }
            public TimeSpan? Duration { get; set; }
        }

        /// <summary>
        /// Add status to history for debugging
        /// </summary>
        /// <param name="status">Status message</param>
        /// <param name="operation">Operation name</param>
        /// <param name="duration">Operation duration (optional)</param>
        public static void AddToHistory(string status, string operation = null, TimeSpan? duration = null)
        {
            try
            {
                lock (_statusHistory)
                {
                    _statusHistory.Add(new StatusHistoryEntry
                    {
                        Timestamp = DateTime.Now,
                        Status = status,
                        Operation = operation,
                        Duration = duration
                    });

                    // Keep only recent entries
                    if (_statusHistory.Count > MaxHistoryEntries)
                    {
                        _statusHistory.RemoveAt(0);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adding to status history: {ex.Message}");
            }
        }

        /// <summary>
        /// Get recent status history for debugging
        /// </summary>
        /// <param name="count">Number of recent entries to return</param>
        /// <returns>Recent status history</returns>
        public static List<StatusHistoryEntry> GetRecentHistory(int count = 10)
        {
            try
            {
                lock (_statusHistory)
                {
                    // Use Skip and Take instead of TakeLast for .NET Framework compatibility
                    var totalCount = _statusHistory.Count;
                    var skipCount = Math.Max(0, totalCount - count);
                    return _statusHistory.Skip(skipCount).Take(count).ToList();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting status history: {ex.Message}");
                return new List<StatusHistoryEntry>();
            }
        }

        /// <summary>
        /// Clear status history
        /// </summary>
        public static void ClearHistory()
        {
            try
            {
                lock (_statusHistory)
                {
                    _statusHistory.Clear();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing status history: {ex.Message}");
            }
        }

        #endregion
    }
}
