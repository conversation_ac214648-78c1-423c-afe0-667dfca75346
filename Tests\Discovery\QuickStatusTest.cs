using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using ProManage.Modules.Services;

namespace ProManage.Tests.Discovery
{
    /// <summary>
    /// Quick test to verify StatusProgressService behavior
    /// </summary>
    [TestClass]
    public class QuickStatusTest
    {
        [TestInitialize]
        public void TestInitialize()
        {
            StatusProgressService.ClearHistory();
        }

        [TestMethod]
        public void QuickTest_GetRecentHistory_Behavior()
        {
            // Clear history first
            StatusProgressService.ClearHistory();
            
            // Add 15 items
            for (int i = 0; i < 15; i++)
            {
                StatusProgressService.AddToHistory($"Status {i}", $"Operation {i}");
            }

            // Get recent 10
            var history = StatusProgressService.GetRecentHistory(10);

            // Debug output
            Console.WriteLine($"History count: {history.Count}");
            for (int i = 0; i < history.Count; i++)
            {
                Console.WriteLine($"Index {i}: {history[i].Status}");
            }

            // Test expectations
            Assert.AreEqual(10, history.Count);
            
            // The test expects "Status 14" at index [9]
            // This means it expects the most recent items in chronological order
            // Items 5-14 should be returned (the last 10 items)
            Assert.AreEqual("Status 5", history[0].Status);  // First item should be Status 5
            Assert.AreEqual("Status 14", history[9].Status); // Last item should be Status 14
        }

        [TestMethod]
        public void QuickTest_FormatStatus_NullHandling()
        {
            var result1 = StatusProgressService.FormatStatus(null, "test");
            var result2 = StatusProgressService.FormatStatus("template", null);
            var result3 = StatusProgressService.FormatStatusWithCount(null, 1, 2);

            Assert.IsNotNull(result1);
            Assert.IsNotNull(result2);
            Assert.IsNotNull(result3);

            Console.WriteLine($"FormatStatus(null, 'test'): '{result1}'");
            Console.WriteLine($"FormatStatus('template', null): '{result2}'");
            Console.WriteLine($"FormatStatusWithCount(null, 1, 2): '{result3}'");
        }

        [TestMethod]
        public void QuickTest_FormDiscoveryService_CompareFormsWithDatabase()
        {
            try
            {
                // Test that the method exists and can be called
                var result = FormDiscoveryService.CompareFormsWithDatabase(null);
                Assert.IsNotNull(result);
                Console.WriteLine($"FormDiscoveryService.CompareFormsWithDatabase returned: {result.GetType().Name}");
                Console.WriteLine($"HasMismatch: {result.HasMismatch}");
                Console.WriteLine($"Errors count: {result.Errors?.Count ?? 0}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calling FormDiscoveryService.CompareFormsWithDatabase: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
